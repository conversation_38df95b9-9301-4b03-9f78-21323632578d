-- Manual fix for lead answer status workflow triggers
-- This script applies only the necessary changes to fix the workflow trigger issue

-- First, add trigger_type column to workflow_triggers table if it doesn't exist
DO $$
BEGIN
    -- Add trigger_type column to workflow_triggers table
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'workflow_triggers'
        AND column_name = 'trigger_type'
    ) THEN
        ALTER TABLE public.workflow_triggers
        ADD COLUMN trigger_type VARCHAR(50);

        -- Set default values for existing records
        UPDATE public.workflow_triggers
        SET trigger_type = CASE
            WHEN entity_type = 'lead' THEN 'lead_status_change'
            WHEN entity_type = 'case' THEN 'case_status_change'
            ELSE 'unknown'
        END
        WHERE trigger_type IS NULL;

        -- Make the column NOT NULL after setting defaults
        ALTER TABLE public.workflow_triggers
        ALTER COLUMN trigger_type SET NOT NULL;

        RAISE NOTICE 'Added trigger_type column to workflow_triggers table';
    ELSE
        RAISE NOTICE 'trigger_type column already exists in workflow_triggers table';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error adding trigger_type column: %', SQLERRM;
END $$;

-- Update workflow trigger types to include lead answer status changes
DO $$
BEGIN
    ALTER TABLE public.workflows
    DROP CONSTRAINT IF EXISTS workflows_trigger_type_check;

    ALTER TABLE public.workflows
    ADD CONSTRAINT workflows_trigger_type_check
    CHECK (trigger_type IN (
      'lead_status_change',
      'case_status_change',
      'lead_answer_status_change',
      'manual'
    ));

    RAISE NOTICE 'Updated workflows constraint to include lead_answer_status_change';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error updating constraint: %', SQLERRM;
END $$;

-- Update existing trigger functions to include trigger_type
CREATE OR REPLACE FUNCTION public.create_lead_workflow_trigger()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create trigger if status actually changed
  IF TG_OP = 'UPDATE' AND OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO public.workflow_triggers (
      entity_type,
      entity_id,
      old_status,
      new_status,
      company_id,
      trigger_type
    ) VALUES (
      'lead',
      NEW.id,
      OLD.status,
      NEW.status,
      NEW.company_id,
      'lead_status_change'
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

CREATE OR REPLACE FUNCTION public.create_case_workflow_trigger()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create trigger if status actually changed
  IF TG_OP = 'UPDATE' AND OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO public.workflow_triggers (
      entity_type,
      entity_id,
      old_status,
      new_status,
      company_id,
      trigger_type
    ) VALUES (
      'case',
      NEW.id,
      OLD.status,
      NEW.status,
      NEW.company_id,
      'case_status_change'
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Create function to handle lead answer status workflow triggers
CREATE OR REPLACE FUNCTION public.create_lead_answer_status_workflow_trigger()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create trigger if answer status actually changed
  IF TG_OP = 'UPDATE' AND OLD.answer_status IS DISTINCT FROM NEW.answer_status THEN
    INSERT INTO public.workflow_triggers (
      entity_type,
      entity_id,
      old_status,
      new_status,
      company_id,
      trigger_type
    ) VALUES (
      'lead',
      NEW.lead_id,
      OLD.answer_status,
      NEW.answer_status,
      NEW.company_id,
      'lead_answer_status_change'
    );
  ELSIF TG_OP = 'INSERT' THEN
    -- For new answer status records, treat as transition from null to new status
    INSERT INTO public.workflow_triggers (
      entity_type,
      entity_id,
      old_status,
      new_status,
      company_id,
      trigger_type
    ) VALUES (
      'lead',
      NEW.lead_id,
      NULL,
      NEW.answer_status,
      NEW.company_id,
      'lead_answer_status_change'
    );
  END IF;

  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Drop existing trigger if it exists (use different name to avoid conflicts)
DROP TRIGGER IF EXISTS lead_answer_status_workflow_trigger ON public.lead_answer_status;
DROP TRIGGER IF EXISTS lead_answer_status_workflow_automation_trigger ON public.lead_answer_status;

-- Create trigger on lead_answer_status table for INSERT and UPDATE
CREATE TRIGGER lead_answer_status_workflow_automation_trigger
  AFTER INSERT OR UPDATE ON public.lead_answer_status
  FOR EACH ROW
  EXECUTE FUNCTION public.create_lead_answer_status_workflow_trigger();

-- Add comments for documentation
COMMENT ON FUNCTION public.create_lead_answer_status_workflow_trigger()
IS 'Creates workflow triggers when lead answer status changes occur';

COMMENT ON TRIGGER lead_answer_status_workflow_automation_trigger ON public.lead_answer_status
IS 'Automatically creates workflow triggers for lead answer status changes';

-- Verify the trigger is created
SELECT
  'Lead Answer Status Workflow Trigger Verification' as test_name,
  COUNT(*) as trigger_count,
  array_agg(trigger_name) as trigger_names
FROM information_schema.triggers
WHERE event_object_table = 'lead_answer_status'
AND trigger_name LIKE '%workflow%';
