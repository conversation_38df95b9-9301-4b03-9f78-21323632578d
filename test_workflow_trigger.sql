-- Test script to manually create a workflow trigger for lead answer status change
-- This will help us verify if the workflow executor can process lead_answer_status_change triggers

-- First, let's check if the workflow_triggers table has a trigger_type column
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'workflow_triggers' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check if there are any workflows configured for lead_answer_status_change
SELECT id, name, trigger_type, trigger_config, is_active
FROM public.workflows 
WHERE trigger_type = 'lead_answer_status_change'
AND is_active = true;

-- Check if there are any existing workflow triggers
SELECT id, entity_type, entity_id, old_status, new_status, processed, created_at
FROM public.workflow_triggers 
WHERE processed = false
ORDER BY created_at DESC
LIMIT 10;

-- If the trigger_type column doesn't exist, let's add it
-- (This is safe to run multiple times)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'workflow_triggers' 
        AND column_name = 'trigger_type'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.workflow_triggers 
        ADD COLUMN trigger_type VARCHAR(50);
        
        -- Set default values for existing records
        UPDATE public.workflow_triggers 
        SET trigger_type = CASE 
            WHEN entity_type = 'lead' THEN 'lead_status_change'
            WHEN entity_type = 'case' THEN 'case_status_change'
            ELSE 'unknown'
        END
        WHERE trigger_type IS NULL;
        
        RAISE NOTICE 'Added trigger_type column to workflow_triggers table';
    ELSE
        RAISE NOTICE 'trigger_type column already exists';
    END IF;
END $$;

-- Now let's manually create a test workflow trigger for lead answer status change
-- Replace the UUIDs below with actual values from your database

-- First, let's find a lead to test with
SELECT id, full_name, company_id 
FROM public.leads 
WHERE company_id IS NOT NULL
LIMIT 1;

-- Insert a test workflow trigger (you'll need to replace the UUIDs with actual values)
-- INSERT INTO public.workflow_triggers (
--   entity_type,
--   entity_id,
--   old_status,
--   new_status,
--   company_id,
--   trigger_type,
--   processed
-- ) VALUES (
--   'lead',
--   'YOUR_LEAD_ID_HERE',  -- Replace with actual lead ID
--   NULL,
--   'לא ענה 1',
--   'YOUR_COMPANY_ID_HERE',  -- Replace with actual company ID
--   'lead_answer_status_change',
--   false
-- );

-- Check the workflow_triggers table structure after our changes
\d public.workflow_triggers;
