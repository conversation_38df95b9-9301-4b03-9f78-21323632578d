-- Add workflow triggers for lead answer status changes
-- This migration adds database triggers to automatically create workflow triggers
-- when lead answer status changes occur

-- Update workflow trigger types to include lead answer status changes
ALTER TABLE public.workflows
DROP CONSTRAINT IF EXISTS workflows_trigger_type_check;

ALTER TABLE public.workflows
ADD CONSTRAINT workflows_trigger_type_check
CHECK (trigger_type IN (
  'lead_status_change',
  'case_status_change',
  'lead_answer_status_change',
  'manual'
));

-- Create function to handle lead answer status workflow triggers
CREATE OR REPLACE FUNCTION public.create_lead_answer_status_workflow_trigger()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create trigger if answer status actually changed
  IF TG_OP = 'UPDATE' AND OLD.answer_status IS DISTINCT FROM NEW.answer_status THEN
    INSERT INTO public.workflow_triggers (
      entity_type,
      entity_id,
      old_status,
      new_status,
      company_id
    ) VALUES (
      'lead',
      NEW.lead_id,
      OLD.answer_status,
      NEW.answer_status,
      NEW.company_id
    );
  ELSIF TG_OP = 'INSERT' THEN
    -- For new answer status records, treat as transition from null to new status
    INSERT INTO public.workflow_triggers (
      entity_type,
      entity_id,
      old_status,
      new_status,
      company_id
    ) VALUES (
      'lead',
      NEW.lead_id,
      NULL,
      NEW.answer_status,
      NEW.company_id
    );
  END IF;

  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger on lead_answer_status table for INSERT and UPDATE
CREATE TRIGGER lead_answer_status_workflow_trigger
  AFTER INSERT OR UPDATE ON public.lead_answer_status
  FOR EACH ROW
  EXECUTE FUNCTION public.create_lead_answer_status_workflow_trigger();

-- Add comments for documentation
COMMENT ON FUNCTION public.create_lead_answer_status_workflow_trigger()
IS 'Creates workflow triggers when lead answer status changes occur';

COMMENT ON TRIGGER lead_answer_status_workflow_trigger ON public.lead_answer_status
IS 'Automatically creates workflow triggers for lead answer status changes';

-- Verify the trigger is created
SELECT
  'Lead Answer Status Workflow Trigger Verification' as test_name,
  COUNT(*) as trigger_count,
  array_agg(trigger_name) as trigger_names
FROM information_schema.triggers
WHERE event_object_table = 'lead_answer_status'
AND trigger_name LIKE '%workflow%';